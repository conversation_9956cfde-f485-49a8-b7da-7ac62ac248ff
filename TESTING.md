# Jest Snapshot Testing Guide

This document provides comprehensive information about the Jest snapshot tests implemented for the Accordion and Carousel components in the manystack.com project.

## Overview

We have implemented comprehensive Jest snapshot tests for two main components:

1. **Accordion Component** (`app/components/Accordion.tsx`)
2. **Carousel Components** (`components/ui/carousel.tsx` and `app/components/carousel-cards/CarouselCard.tsx`)

## Test Coverage

### Accordion Component Tests

**Location**: `__tests__/components/Accordion.test.tsx`

**Test Scenarios**:
- ✅ Empty state (renders null when items array is empty)
- ✅ Single item accordion in vertical orientation (mobile)
- ✅ Single item accordion in horizontal orientation (xl screen)
- ✅ Multiple items accordion in vertical orientation (mobile)
- ✅ Multiple items accordion in horizontal orientation (xl screen)
- ✅ Screen size variations (undefined screen size)
- ✅ Content variations (long text content)
- ✅ Special characters and formatting

**Total Tests**: 9 tests with 8 snapshots

### Accordion Width Calculation Tests

**Location**: `__tests__/components/AccordionWidthCalculationSimple.test.tsx`

**Test Scenarios**:
- ✅ Screen size behavior (horizontal on xl, vertical on mobile)
- ✅ Width calculation scenarios (1400px, 1300px, 1920px screen widths)
- ✅ Resize behavior (small to large, large to small screen resize)
- ✅ Screen size transitions (mobile ↔ xl screen transitions)
- ✅ Different item configurations (single item, many items)

**Key Features Tested**:
- **Width Calculation Logic**: Tests the core formula `contentWidth = accordionWidth - (itemWidth * numberOfItems)`
- **Screen Size Transitions**: Ensures proper layout changes when crossing the 1280px breakpoint
- **Resize Handling**: Verifies width recalculation on screen resize (only on xl screens)
- **Edge Cases**: Single item, many items, various screen widths

**Total Tests**: 11 tests with 11 snapshots

### Carousel Component Tests

**Location**: `__tests__/components/Carousel.test.tsx`

**Test Scenarios**:
- ✅ Basic carousel with horizontal orientation (default)
- ✅ Carousel with vertical orientation
- ✅ Carousel with custom options
- ✅ Single slide carousel
- ✅ Multiple slides carousel
- ✅ Carousel with navigation controls
- ✅ Carousel with image content
- ✅ Carousel with complex content
- ✅ Vertical carousel with navigation
- ✅ Carousel with custom classes

**Total Tests**: 10 tests with 10 snapshots

### CarouselCard Component Tests

**Location**: `__tests__/components/CarouselCard.test.tsx`

**Test Scenarios**:
- ✅ Basic rendering with single child
- ✅ Basic rendering with multiple children
- ✅ Carousel card with title
- ✅ Carousel card with title and multiple children
- ✅ Carousel card without title
- ✅ Carousel card with overlay
- ✅ Carousel card with title and overlay
- ✅ Carousel card with custom className
- ✅ Carousel card with custom className and title
- ✅ Navigation visibility (single vs multiple children)
- ✅ Event handlers
- ✅ Complex nested content scenarios

**Total Tests**: 13 tests with 13 snapshots

## Running Tests

### Run All Tests
```bash
npm test
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Run Specific Test File
```bash
npm test __tests__/components/Accordion.test.tsx
npm test __tests__/components/AccordionWidthCalculationSimple.test.tsx
npm test __tests__/components/Carousel.test.tsx
npm test __tests__/components/CarouselCard.test.tsx
```

### Update Snapshots
When component changes are intentional and snapshots need to be updated:
```bash
npm run test:update-snapshots
```

Or for a specific test file:
```bash
npm test __tests__/components/Accordion.test.tsx -- --updateSnapshot
npm test __tests__/components/AccordionWidthCalculationSimple.test.tsx -- --updateSnapshot
```

## Test Configuration

### Jest Configuration
- **Config File**: `jest.config.js`
- **Setup File**: `jest.setup.js`
- **Test Environment**: jsdom
- **Module Mapping**: `@/*` maps to `<rootDir>/*`

### Mocked Dependencies
The tests mock several dependencies to ensure consistent and isolated testing:

- **lucide-react icons**: Mocked as simple SVG elements with test IDs
- **embla-carousel-react**: Mocked to return controllable carousel API
- **usehooks-ts**: Mocked resize observer and window size hooks
- **lodash**: Mocked min function
- **Custom hooks**: Mocked useIsXlScreen hook
- **Browser APIs**: ResizeObserver, IntersectionObserver, matchMedia, getBoundingClientRect

## Snapshot Files

Snapshots are automatically generated and stored in:
- `__tests__/components/__snapshots__/Accordion.test.tsx.snap`
- `__tests__/components/__snapshots__/AccordionWidthCalculationSimple.test.tsx.snap`
- `__tests__/components/__snapshots__/Carousel.test.tsx.snap`
- `__tests__/components/__snapshots__/CarouselCard.test.tsx.snap`

## Best Practices

### When to Update Snapshots
- ✅ When you intentionally change component structure or styling
- ✅ When you add new props or functionality
- ✅ When you fix bugs that affect component output
- ❌ Never update snapshots without reviewing the changes first

### Reviewing Snapshot Changes
1. Run tests to see which snapshots failed
2. Review the diff to understand what changed
3. Verify the changes are intentional and correct
4. Update snapshots only if changes are expected

### Adding New Tests
When adding new test scenarios:
1. Follow the existing naming conventions
2. Group related tests in describe blocks
3. Use descriptive test names that explain the scenario
4. Mock external dependencies appropriately
5. Test different prop combinations and edge cases

## Troubleshooting

### Common Issues

**Tests failing after component changes**:
- Review the snapshot diff
- If changes are intentional, update snapshots
- If changes are unintentional, fix the component

**Mock-related errors**:
- Ensure all external dependencies are properly mocked
- Check that mock implementations match expected interfaces

**Import/module errors**:
- Verify module mapping in jest.config.js
- Ensure all imports use the correct paths

### Getting Help
If you encounter issues with the tests:
1. Check the Jest documentation: https://jestjs.io/docs/snapshot-testing
2. Review the existing test files for patterns
3. Ensure your development environment matches the project setup

## Summary

The snapshot tests provide comprehensive coverage of the Accordion and Carousel components, ensuring that:
- Visual states are preserved across different configurations
- Component behavior is consistent across screen sizes
- Edge cases and error states are handled properly
- Future changes don't inadvertently break existing functionality

**Total Test Coverage**: 43 tests with 42 snapshots across 4 test files
