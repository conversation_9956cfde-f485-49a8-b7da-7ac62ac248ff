// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Accordion Width Calculation Additional snapshots for different container sizes should match snapshot for medium desktop (1600px) with 5 items 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1r:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r1q:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1q:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r1r:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1t:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1s:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1s:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1t:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1v:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1u:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1u:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1v:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r21:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r20:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r20:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r21:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r23:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r22:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r22:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r23:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation Additional snapshots for different container sizes should match snapshot for small desktop (1300px) with 3 items 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1l:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r1k:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1k:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r1l:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1n:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1m:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1m:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1n:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1p:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1o:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1o:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1p:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation Additional snapshots for different container sizes should match snapshot for ultra-wide (2560px) with 7 items 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r25:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r24:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r24:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r25:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r27:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r26:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r26:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r27:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r29:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r28:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r28:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r29:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r2b:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r2a:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r2a:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r2b:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r2d:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r2c:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r2c:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r2d:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r2f:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r2e:"
          type="button"
        >
          Test Item 6
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r2e:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r2f:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r2h:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r2g:"
          type="button"
        >
          Test Item 7
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r2g:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r2h:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation Edge cases and boundary conditions should handle many items with snapshot 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r4n:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r4m:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4m:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r4n:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4p:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4o:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4o:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4p:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4r:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4q:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4q:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4r:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4t:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4s:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4s:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4t:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4v:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4u:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4u:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4v:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r51:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r50:"
          type="button"
        >
          Test Item 6
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r50:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r51:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r53:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r52:"
          type="button"
        >
          Test Item 7
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r52:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r53:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r55:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r54:"
          type="button"
        >
          Item 8
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r54:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r55:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r57:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r56:"
          type="button"
        >
          Item 9
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r56:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r57:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r59:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r58:"
          type="button"
        >
          Item 10
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r58:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r59:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r5b:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r5a:"
          type="button"
        >
          Item 11
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r5a:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r5b:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r5d:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r5c:"
          type="button"
        >
          Item 12
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r5c:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r5d:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation Edge cases and boundary conditions should handle single item with snapshot 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r4l:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r4k:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4k:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r4l:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation Float precision snapshots should match snapshot for complex float (1920.333px) 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r4b:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r4a:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4a:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r4b:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4d:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4c:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4c:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4d:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4f:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4e:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4e:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4f:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4h:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4g:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4g:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4h:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r4j:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4i:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4i:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r4j:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation Float precision snapshots should match snapshot for float precision case (1600.125px) 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r3f:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r3e:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3e:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r3f:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3h:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r3g:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3g:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3h:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3j:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r3i:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3i:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3j:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3l:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r3k:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3k:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3l:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3n:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r3m:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3m:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3n:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3p:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r3o:"
          type="button"
        >
          Test Item 6
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3o:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3p:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3r:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r3q:"
          type="button"
        >
          Test Item 7
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3q:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3r:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation Float precision snapshots should match snapshot for precise float (1398.75px) 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r3t:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r3s:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3s:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r3t:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3v:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r3u:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r3u:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3v:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r41:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r40:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r40:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r41:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r43:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r42:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r42:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r43:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r45:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r44:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r44:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r45:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r47:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r46:"
          type="button"
        >
          Test Item 6
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r46:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r47:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r49:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r48:"
          type="button"
        >
          Test Item 7
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r48:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r49:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation ScrollArea width calculation snapshots should render with calculated width for 1400px container with 3 items 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r0:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r0:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r1:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r2:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r2:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r5:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r5:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation ScrollArea width calculation snapshots should render with calculated width for 1600px container with 8 items 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:rh:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:rg:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rg:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:rh:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rj:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:ri:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ri:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rj:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rl:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rk:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rk:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rl:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rn:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rm:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rm:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rn:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rp:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:ro:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ro:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rp:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rr:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rq:"
          type="button"
        >
          Test Item 6
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rq:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rr:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rt:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rs:"
          type="button"
        >
          Test Item 7
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rs:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rt:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rv:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:ru:"
          type="button"
        >
          Test Item 8
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ru:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rv:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation ScrollArea width calculation snapshots should render with calculated width for 1920px container with 5 items 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r7:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r6:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r6:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r7:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r9:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r8:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r8:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r9:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rb:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:ra:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ra:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rb:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rd:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rc:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rc:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rd:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rf:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:re:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:re:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rf:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation ScrollArea width calculation snapshots should render with calculated width for ultra-wide 2560px container 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r11:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r10:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r10:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r11:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Content 1
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r13:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r12:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r12:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r13:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r15:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r14:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r14:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r15:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r17:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r16:"
          type="button"
        >
          Test Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r16:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r17:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r19:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r18:"
          type="button"
        >
          Test Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r18:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r19:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1b:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1a:"
          type="button"
        >
          Test Item 6
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1a:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1b:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1d:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1c:"
          type="button"
        >
          Test Item 7
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1c:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1d:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1f:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1e:"
          type="button"
        >
          Test Item 8
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1e:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1f:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1h:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1g:"
          type="button"
        >
          Test Item 9
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1g:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1h:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1j:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1i:"
          type="button"
        >
          Test Item 10
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1i:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1j:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 29px;"
      />
    </div>
  </div>
</div>
`;
