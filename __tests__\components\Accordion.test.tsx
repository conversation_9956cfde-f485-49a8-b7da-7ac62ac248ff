import React from 'react'
import { render } from '@testing-library/react'
import Accordion from '@/app/components/Accordion'

// Mock the hooks and dependencies
jest.mock('@/app/hooks/useIsXlScreen', () => ({
  useIsXlScreen: jest.fn(),
}))

jest.mock('usehooks-ts', () => ({
  useResizeObserver: jest.fn(),
}))

jest.mock('lodash', () => ({
  min: jest.fn(arr => Math.min(...arr.filter(Boolean))),
}))

jest.mock('@/lib/getInnerWidth', () => ({
  getInnerWidth: jest.fn(() => 800),
}))

// Import the mocked functions
import { useIsXlScreen } from '@/app/hooks/useIsXlScreen'
import { useResizeObserver } from 'usehooks-ts'

const mockUseIsXlScreen = useIsXlScreen as jest.MockedFunction<typeof useIsXlScreen>
const mockUseResizeObserver = useResizeObserver as jest.MockedFunction<typeof useResizeObserver>

// Mock data for testing
const mockSingleItem = [
  {
    id: 'item-1',
    title: 'Test Item 1',
    text: 'This is the content for test item 1.',
  },
]

const mockMultipleItems = [
  {
    id: 'item-1',
    title: 'Test Item 1',
    text: 'This is the content for test item 1.',
  },
  {
    id: 'item-2',
    title: 'Test Item 2',
    text: 'This is the content for test item 2.',
  },
  {
    id: 'item-3',
    title: 'Test Item 3',
    text: 'This is the content for test item 3.',
  },
]

describe('Accordion Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseResizeObserver.mockImplementation(() => ({ width: 0, height: 0 }))
  })

  describe('Empty state', () => {
    it('should render null when items array is empty', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={[]} />)

      expect(container.firstChild).toBeNull()
    })

    it('should match snapshot for empty items array', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={[]} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Single item accordion', () => {
    it('should render single item in vertical orientation (mobile)', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={mockSingleItem} />)

      expect(container).toMatchSnapshot()
    })

    it('should render single item in horizontal orientation (xl screen)', () => {
      mockUseIsXlScreen.mockReturnValue(true)

      const { container } = render(<Accordion items={mockSingleItem} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Multiple items accordion', () => {
    it('should render multiple items in vertical orientation (mobile)', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should render multiple items in horizontal orientation (xl screen)', () => {
      mockUseIsXlScreen.mockReturnValue(true)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Screen size variations', () => {
    it('should render correctly when screen size is undefined', () => {
      mockUseIsXlScreen.mockReturnValue(undefined)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Content variations', () => {
    it('should render with long text content', () => {
      const longTextItems = [
        {
          id: 'long-item',
          title: 'Item with Long Content',
          text: 'This is a very long text content that should test how the accordion handles extensive text. '.repeat(
            10
          ),
        },
      ]

      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={longTextItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should render with special characters and formatting', () => {
      const specialItems = [
        {
          id: 'special-item',
          title: 'Special Characters & Formatting',
          text: 'Text with\nnew lines\n\nAnd special chars: @#$%^&*()',
        },
      ]

      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={specialItems} />)

      expect(container).toMatchSnapshot()
    })
  })
})
