// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Carousel Components Basic Carousel should render carousel with custom options 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Slide 1
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Components Basic Carousel should render carousel with horizontal orientation by default 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Slide 1
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Components Basic Carousel should render carousel with vertical orientation 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -mt-4 flex-col"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pt-4"
          role="group"
        >
          <div>
            Slide 1
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Components Carousel orientations should render vertical carousel with navigation 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -mt-4 flex-col"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pt-4"
          role="group"
        >
          <div>
            Vertical Slide 1
          </div>
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pt-4"
          role="group"
        >
          <div>
            Vertical Slide 2
          </div>
        </div>
      </div>
    </div>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -top-12 left-1/2 -translate-x-1/2 rotate-90"
    >
      <svg
        data-testid="arrow-left"
      />
      <span
        class="sr-only"
      >
        Previous slide
      </span>
    </button>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -bottom-12 left-1/2 -translate-x-1/2 rotate-90"
    >
      <svg
        data-testid="arrow-right"
      />
      <span
        class="sr-only"
      >
        Next slide
      </span>
    </button>
  </div>
</div>
`;

exports[`Carousel Components Carousel with custom classes should render carousel with custom className 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative custom-carousel-class"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4 custom-content-class"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4 custom-item-class"
          role="group"
        >
          <div>
            Custom Styled Slide
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Components Carousel with different content types should render carousel with complex content 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div
            class="p-4"
          >
            <h3>
              Card Title 1
            </h3>
            <p>
              Card description with some text content.
            </p>
            <button>
              Action Button
            </button>
          </div>
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div
            class="p-4"
          >
            <h3>
              Card Title 2
            </h3>
            <p>
              Another card with different content.
            </p>
            <button>
              Another Button
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Components Carousel with different content types should render carousel with image content 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <img
            alt="Test Image 1"
            src="/test-image-1.jpg"
          />
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <img
            alt="Test Image 2"
            src="/test-image-2.jpg"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Components Multiple slides carousel should render carousel with multiple slides 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Slide 1
          </div>
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Slide 2
          </div>
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Slide 3
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Components Multiple slides carousel should render carousel with navigation controls 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Slide 1
          </div>
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Slide 2
          </div>
        </div>
      </div>
    </div>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -left-12 top-1/2 -translate-y-1/2"
    >
      <svg
        data-testid="arrow-left"
      />
      <span
        class="sr-only"
      >
        Previous slide
      </span>
    </button>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2"
    >
      <svg
        data-testid="arrow-right"
      />
      <span
        class="sr-only"
      >
        Next slide
      </span>
    </button>
  </div>
</div>
`;

exports[`Carousel Components Single slide carousel should render carousel with single slide 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    role="region"
  >
    <div
      class="overflow-hidden"
    >
      <div
        class="flex -ml-4"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          role="group"
        >
          <div>
            Single Slide Content
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
