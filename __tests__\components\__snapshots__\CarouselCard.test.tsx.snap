// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`CarouselCard Component Basic rendering should render carousel card with multiple children 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Child 1
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Child 2
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Child 3
              </div>
            </article>
          </div>
        </div>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Basic rendering should render carousel card with single child 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Single Child Content
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Complex content scenarios should render carousel card with complex nested content 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-[auto_1fr]"
    role="region"
  >
    <h2
      class="font-bold text-xl py-6"
    >
      Complex Content Carousel
    </h2>
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div
                class="p-4 bg-white rounded shadow"
              >
                <h3
                  class="text-lg font-bold"
                >
                  Card 1
                </h3>
                <p
                  class="text-gray-600"
                >
                  Description for card 1
                </p>
                <button
                  class="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
                >
                  Action
                </button>
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div
                class="p-4 bg-gray-100 rounded shadow"
              >
                <h3
                  class="text-lg font-bold"
                >
                  Card 2
                </h3>
                <p
                  class="text-gray-600"
                >
                  Description for card 2
                </p>
                <div
                  class="flex gap-2 mt-2"
                >
                  <button
                    class="px-3 py-1 bg-green-500 text-white rounded"
                  >
                    Yes
                  </button>
                  <button
                    class="px-3 py-1 bg-red-500 text-white rounded"
                  >
                    No
                  </button>
                </div>
              </div>
            </article>
          </div>
        </div>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Event handlers should render carousel card with event handlers 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Content with event handlers
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Another item
              </div>
            </article>
          </div>
        </div>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Navigation visibility should not show navigation controls with single child 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Single item - no navigation needed
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Navigation visibility should show navigation controls with multiple children 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 1
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 2
              </div>
            </article>
          </div>
        </div>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With custom className should render carousel card with custom className 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1 custom-carousel-card"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Custom styled content
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With custom className should render carousel card with custom className and title 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid grid-rows-[auto_1fr] w-full h-96"
    role="region"
  >
    <h2
      class="font-bold text-xl py-6"
    >
      Custom Styled Carousel
    </h2>
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 1
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 2
              </div>
            </article>
          </div>
        </div>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With overlay should render carousel card with overlay 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Content with overlay
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Another item
              </div>
            </article>
          </div>
        </div>
      </div>
      <div
        class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      >
        <span
          class="text-white"
        >
          Overlay Content
        </span>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With overlay should render carousel card with title and overlay 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-[auto_1fr]"
    role="region"
  >
    <h2
      class="font-bold text-xl py-6"
    >
      Carousel with Overlay
    </h2>
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 1
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 2
              </div>
            </article>
          </div>
        </div>
      </div>
      <div
        class="absolute top-0 right-0 p-2"
      >
        <button>
          Close
        </button>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With title should render carousel card with title 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-[auto_1fr]"
    role="region"
  >
    <h2
      class="font-bold text-xl py-6"
    >
      Test Carousel Title
    </h2>
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Content with title
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component With title should render carousel card with title and multiple children 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-[auto_1fr]"
    role="region"
  >
    <h2
      class="font-bold text-xl py-6"
    >
      Multiple Items Carousel
    </h2>
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 1
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 2
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Item 3
              </div>
            </article>
          </div>
        </div>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`CarouselCard Component Without title should render carousel card without title 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative grid w-80 h-80 grid-rows-1"
    role="region"
  >
    <div
      class="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full top-1/2 -translate-y-1/2 z-10 left-3 !pointer-events-auto"
      >
        <svg
          data-testid="arrow-left"
        />
        <span
          class="sr-only"
        >
          Previous slide
        </span>
      </button>
      <div
        class="overflow-hidden"
      >
        <div
          class="-ml-4 flex items-center h-full"
        >
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Content without title
              </div>
            </article>
          </div>
          <div
            aria-roledescription="slide"
            class="min-w-0 shrink-0 grow-0 basis-full pl-4 h-full select-none"
            role="group"
          >
            <article
              class="rounded *:p-8 [&>a]:inline-block h-full [&>div]:h-full [&>a:has(div)]:h-full [&>a>div]:h-full"
            >
              <div>
                Another item
              </div>
            </article>
          </div>
        </div>
      </div>
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground absolute h-8 w-8 rounded-full -right-12 top-1/2 -translate-y-1/2 z-10 left-[calc(100%-2.75rem)] !pointer-events-auto"
      >
        <svg
          data-testid="arrow-right"
        />
        <span
          class="sr-only"
        >
          Next slide
        </span>
      </button>
    </div>
  </div>
</div>
`;
