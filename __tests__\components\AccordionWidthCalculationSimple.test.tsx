import React from 'react'
import { render } from '@testing-library/react'
import Accordion from '@/app/components/Accordion'

// Mock the hooks and dependencies
jest.mock('@/app/hooks/useIsXlScreen', () => ({
  useIsXlScreen: jest.fn(),
}))

jest.mock('usehooks-ts', () => ({
  useResizeObserver: jest.fn(),
}))

jest.mock('lodash', () => ({
  min: jest.fn(arr => Math.min(...arr.filter(Boolean))),
}))

jest.mock('@/lib/getInnerWidth', () => ({
  getInnerWidth: jest.fn(),
}))

// Import the mocked functions
import { useIsXlScreen } from '@/app/hooks/useIsXlScreen'

import { getInnerWidth } from '@/lib/getInnerWidth'

const mockUseIsXlScreen = useIsXlScreen as jest.MockedFunction<typeof useIsXlScreen>
const mockGetInnerWidth = getInnerWidth as jest.MockedFunction<typeof getInnerWidth>

// Mock data for testing - 7 items as mentioned in requirements
const mockSevenItems = [
  { id: 'item-1', title: 'Test Item 1', text: 'Content 1' },
  { id: 'item-2', title: 'Test Item 2', text: 'Content 2' },
  { id: 'item-3', title: 'Test Item 3', text: 'Content 3' },
  { id: 'item-4', title: 'Test Item 4', text: 'Content 4' },
  { id: 'item-5', title: 'Test Item 5', text: 'Content 5' },
  { id: 'item-6', title: 'Test Item 6', text: 'Content 6' },
  { id: 'item-7', title: 'Test Item 7', text: 'Content 7' },
]

describe('Accordion Width Calculation', () => {
  // Fixed values as per requirements
  const TRIGGER_WIDTH = 28 // min-w-7 in Tailwind
  const ITEM_BORDER = 1 // border-right 1px
  const ITEM_TOTAL_WIDTH = TRIGGER_WIDTH + ITEM_BORDER // 29px total

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock getBoundingClientRect to return the fixed item width (trigger + border)
    Element.prototype.getBoundingClientRect = jest.fn(() => ({
      width: ITEM_TOTAL_WIDTH, // 29px (28px trigger + 1px border)
      height: 120,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
      toJSON: jest.fn(),
    }))
  })

  describe('ScrollArea width calculation snapshots', () => {
    it('should render with calculated width for 1400px container with 3 items', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1400.5)

      const threeItems = mockSevenItems.slice(0, 3)
      const { container } = render(<Accordion items={threeItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should render with calculated width for 1920px container with 5 items', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1920.75)

      const fiveItems = mockSevenItems.slice(0, 5)
      const { container } = render(<Accordion items={fiveItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should render with calculated width for 1600px container with 8 items', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1600.25)

      const eightItems = [
        ...mockSevenItems,
        { id: 'item-8', title: 'Test Item 8', text: 'Content 8' },
      ]
      const { container } = render(<Accordion items={eightItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should render with calculated width for ultra-wide 2560px container', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(2560.0)

      const tenItems = [
        ...mockSevenItems,
        { id: 'item-8', title: 'Test Item 8', text: 'Content 8' },
        { id: 'item-9', title: 'Test Item 9', text: 'Content 9' },
        { id: 'item-10', title: 'Test Item 10', text: 'Content 10' },
      ]
      const { container } = render(<Accordion items={tenItems} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Component structure verification for different container sizes', () => {
    it('should render correctly for small desktop (1300px) with 3 items', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1300.5)

      const threeItems = mockSevenItems.slice(0, 3)
      const { container } = render(<Accordion items={threeItems} />)

      // Should be in horizontal orientation (xl screen)
      const accordionContainer = container.querySelector('[data-orientation="horizontal"]')
      expect(accordionContainer).toBeTruthy()

      // Should have correct number of items
      const accordionItems = container.querySelectorAll('[data-orientation="horizontal"][data-state]')
      expect(accordionItems).toHaveLength(3)
    })

    it('should render correctly for medium desktop (1600px) with 5 items', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1600.25)

      const fiveItems = mockSevenItems.slice(0, 5)
      const { container } = render(<Accordion items={fiveItems} />)

      // Should be in horizontal orientation (xl screen)
      const accordionContainer = container.querySelector('[data-orientation="horizontal"]')
      expect(accordionContainer).toBeTruthy()

      // Should have correct number of items
      const accordionItems = container.querySelectorAll('[data-orientation="horizontal"][data-state]')
      expect(accordionItems).toHaveLength(5)
    })

    it('should render correctly for ultra-wide (2560px) with 7 items', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(2560.0)

      const { container } = render(<Accordion items={mockSevenItems} />)

      // Should be in horizontal orientation (xl screen)
      const accordionContainer = container.querySelector('[data-orientation="horizontal"]')
      expect(accordionContainer).toBeTruthy()

      // Should have correct number of items
      const accordionItems = container.querySelectorAll('[data-orientation="horizontal"][data-state]')
      expect(accordionItems).toHaveLength(7)
    })
  })

  describe('Screen size breakpoint behavior (>1280px requirement)', () => {
    it('should not calculate width below 1280px breakpoint', () => {
      // Below 1280px - should use mobile behavior
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={mockSevenItems} />)
      const scrollArea = container.querySelector('[class*="h-full"]')
      const styleAttribute = scrollArea?.getAttribute('style')

      // Should use 100% width, not calculated width
      expect(styleAttribute).toContain('width: 100%')

      // Should be in vertical orientation
      const accordionContainer = container.querySelector('[data-orientation="vertical"]')
      expect(accordionContainer).toBeTruthy()
    })

    it('should calculate width above 1280px breakpoint', () => {
      // Above 1280px - should use calculated width
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1300.5) // Just above breakpoint

      const { container } = render(<Accordion items={mockSevenItems} />)

      // Should be in horizontal orientation (xl screen)
      const accordionContainer = container.querySelector('[data-orientation="horizontal"]')
      expect(accordionContainer).toBeTruthy()

      // ScrollArea should exist and be ready for width calculation
      const scrollArea = container.querySelector('[class*="h-full"]')
      expect(scrollArea).toBeTruthy()
    })
  })

  describe('Float precision snapshots', () => {
    it('should match snapshot for float precision case (1600.125px)', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1600.125)

      const { container } = render(<Accordion items={mockSevenItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should match snapshot for precise float (1398.75px)', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1398.75)

      const { container } = render(<Accordion items={mockSevenItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should match snapshot for complex float (1920.333px)', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1920.333)

      const fiveItems = mockSevenItems.slice(0, 5)
      const { container } = render(<Accordion items={fiveItems} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Edge cases and boundary conditions', () => {
    it('should handle single item with snapshot', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1500.5)

      const singleItem = [mockSevenItems[0]]
      const { container } = render(<Accordion items={singleItem} />)

      expect(container).toMatchSnapshot()
    })

    it('should verify single item structure', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1500.5)

      const singleItem = [mockSevenItems[0]]
      const { container } = render(<Accordion items={singleItem} />)

      // Should be in horizontal orientation (xl screen)
      const accordionContainer = container.querySelector('[data-orientation="horizontal"]')
      expect(accordionContainer).toBeTruthy()

      // Should have exactly one item
      const accordionItems = container.querySelectorAll(
        '[data-orientation="horizontal"][data-state]'
      )
      expect(accordionItems).toHaveLength(1)
    })

    it('should handle many items with snapshot', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(2560.0)

      const manyItems = [
        ...mockSevenItems,
        { id: 'item-8', title: 'Item 8', text: 'Content 8' },
        { id: 'item-9', title: 'Item 9', text: 'Content 9' },
        { id: 'item-10', title: 'Item 10', text: 'Content 10' },
        { id: 'item-11', title: 'Item 11', text: 'Content 11' },
        { id: 'item-12', title: 'Item 12', text: 'Content 12' },
      ]
      const { container } = render(<Accordion items={manyItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should verify many items width calculation', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(2560.0)

      const twelveItems = [
        ...mockSevenItems,
        { id: 'item-8', title: 'Item 8', text: 'Content 8' },
        { id: 'item-9', title: 'Item 9', text: 'Content 9' },
        { id: 'item-10', title: 'Item 10', text: 'Content 10' },
        { id: 'item-11', title: 'Item 11', text: 'Content 11' },
        { id: 'item-12', title: 'Item 12', text: 'Content 12' },
      ]
      const { container } = render(<Accordion items={twelveItems} />)

      const scrollArea = container.querySelector('[class*="h-full"]')
      const styleAttribute = scrollArea?.getAttribute('style')

      // Should contain calculated width (2560 - 29*12 = 2212)
      expect(styleAttribute).toContain('width: 2212px')
    })
  })
})
