import React from 'react'
import { render } from '@testing-library/react'
import Accordion from '@/app/components/Accordion'

// Mock the hooks and dependencies
jest.mock('@/app/hooks/useIsXlScreen', () => ({
  useIsXlScreen: jest.fn(),
}))

jest.mock('usehooks-ts', () => ({
  useResizeObserver: jest.fn(),
}))

jest.mock('lodash', () => ({
  min: jest.fn(arr => Math.min(...arr.filter(Boolean))),
}))

jest.mock('@/lib/getInnerWidth', () => ({
  getInnerWidth: jest.fn(),
}))

// Import the mocked functions
import { useIsXlScreen } from '@/app/hooks/useIsXlScreen'

import { getInnerWidth } from '@/lib/getInnerWidth'

const mockUseIsXlScreen = useIsXlScreen as jest.MockedFunction<typeof useIsXlScreen>
const mockGetInnerWidth = getInnerWidth as jest.MockedFunction<typeof getInnerWidth>

// Mock data for testing - 7 items as mentioned in requirements
const mockSevenItems = [
  { id: 'item-1', title: 'Test Item 1', text: 'Content 1' },
  { id: 'item-2', title: 'Test Item 2', text: 'Content 2' },
  { id: 'item-3', title: 'Test Item 3', text: 'Content 3' },
  { id: 'item-4', title: 'Test Item 4', text: 'Content 4' },
  { id: 'item-5', title: 'Test Item 5', text: 'Content 5' },
  { id: 'item-6', title: 'Test Item 6', text: 'Content 6' },
  { id: 'item-7', title: 'Test Item 7', text: 'Content 7' },
]

describe('Accordion Width Calculation', () => {
  // Fixed values as per requirements
  const TRIGGER_WIDTH = 28 // min-w-7 in Tailwind
  const ITEM_BORDER = 1 // border-right 1px
  const ITEM_TOTAL_WIDTH = TRIGGER_WIDTH + ITEM_BORDER // 29px total

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock getBoundingClientRect to return the fixed item width (trigger + border)
    Element.prototype.getBoundingClientRect = jest.fn(() => ({
      width: ITEM_TOTAL_WIDTH, // 29px (28px trigger + 1px border)
      height: 120,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
      toJSON: jest.fn(),
    }))
  })

  describe('Width calculation formula verification', () => {
    it('should calculate content width using exact Accordion.tsx formula', () => {
      // Formula from Accordion.tsx line 30:
      // const contentWidth = accordionWidth - itemWidthRef.current * items.length

      const testCases = [
        { accordionWidth: 1400.5, itemCount: 3, expected: 1400.5 - 29 * 3 },
        { accordionWidth: 1920.75, itemCount: 5, expected: 1920.75 - 29 * 5 },
        { accordionWidth: 1600.25, itemCount: 8, expected: 1600.25 - 29 * 8 },
        { accordionWidth: 1280.1, itemCount: 1, expected: 1280.1 - 29 * 1 }, // Just above breakpoint
        { accordionWidth: 2560.0, itemCount: 10, expected: 2560.0 - 29 * 10 },
      ]

      testCases.forEach(({ accordionWidth, itemCount, expected }) => {
        const calculatedWidth = accordionWidth - ITEM_TOTAL_WIDTH * itemCount
        expect(calculatedWidth).toBeCloseTo(expected, 2)
      })
    })

    it('should handle float precision correctly', () => {
      // Test with float values like real measurements
      const accordionWidth = 1398.75
      const itemCount = 7
      const expectedContentWidth = accordionWidth - ITEM_TOTAL_WIDTH * itemCount

      expect(expectedContentWidth).toBeCloseTo(1195.75, 2)
    })
  })

  describe('Different AccordionContainer sizes', () => {
    const testScenarios = [
      { name: 'Small desktop', width: 1300.5, items: 3 },
      { name: 'Medium desktop', width: 1600.25, items: 5 },
      { name: 'Large desktop', width: 1920.75, items: 7 },
      { name: 'Ultra-wide', width: 2560.0, items: 10 },
      { name: 'Custom size', width: 1450.33, items: 4 },
    ]

    testScenarios.forEach(({ name, width, items }) => {
      it(`should calculate correctly for ${name} (${width}px with ${items} items)`, () => {
        const expectedContentWidth = width - ITEM_TOTAL_WIDTH * items

        // Verify the calculation
        expect(expectedContentWidth).toBeCloseTo(width - 29 * items, 2)

        // Ensure the result is a valid positive number
        expect(expectedContentWidth).toBeGreaterThan(0)
      })
    })
  })

  describe('Screen size breakpoint behavior (>1280px requirement)', () => {
    it('should not calculate width below 1280px breakpoint', () => {
      // Below 1280px - should use mobile behavior
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={mockSevenItems} />)
      const scrollArea = container.querySelector('[class*="h-full"]')
      const styleAttribute = scrollArea?.getAttribute('style')

      // Should use 100% width, not calculated width
      expect(styleAttribute).toContain('width: 100%')

      // Should be in vertical orientation
      const accordionContainer = container.querySelector('[data-orientation="vertical"]')
      expect(accordionContainer).toBeTruthy()
    })

    it('should calculate width above 1280px breakpoint', () => {
      // Above 1280px - should use calculated width
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1300.5) // Just above breakpoint

      const { container } = render(<Accordion items={mockSevenItems} />)

      // Should be in horizontal orientation (xl screen)
      const accordionContainer = container.querySelector('[data-orientation="horizontal"]')
      expect(accordionContainer).toBeTruthy()

      // ScrollArea should exist and be ready for width calculation
      const scrollArea = container.querySelector('[class*="h-full"]')
      expect(scrollArea).toBeTruthy()
    })
  })

  describe('Float precision and real-world measurements', () => {
    it('should handle precise float measurements', () => {
      const testCases = [
        { accordionWidth: 1398.75, itemCount: 7, expected: 1398.75 - 203 },
        { accordionWidth: 1920.333, itemCount: 5, expected: 1920.333 - 145 },
        { accordionWidth: 1600.125, itemCount: 8, expected: 1600.125 - 232 },
        { accordionWidth: 2560.999, itemCount: 12, expected: 2560.999 - 348 },
      ]

      testCases.forEach(({ accordionWidth, itemCount, expected }) => {
        const calculatedWidth = accordionWidth - ITEM_TOTAL_WIDTH * itemCount
        expect(calculatedWidth).toBeCloseTo(expected, 3)
      })
    })

    it('should verify fixed constants are correct', () => {
      // These should always be the same as per requirements
      expect(TRIGGER_WIDTH).toBe(28) // min-w-7
      expect(ITEM_BORDER).toBe(1) // border-right 1px
      expect(ITEM_TOTAL_WIDTH).toBe(29) // total per item
    })
  })

  describe('Edge cases and boundary conditions', () => {
    it('should handle single item scenarios', () => {
      const singleItemCases = [
        { width: 1280.1, expected: 1280.1 - 29 },
        { width: 1500.5, expected: 1500.5 - 29 },
        { width: 2000.0, expected: 2000.0 - 29 },
      ]

      singleItemCases.forEach(({ width, expected }) => {
        const result = width - ITEM_TOTAL_WIDTH * 1
        expect(result).toBeCloseTo(expected, 2)
      })
    })

    it('should handle many items scenarios', () => {
      const manyItemsCases = [
        { width: 1920.0, itemCount: 15, expected: 1920.0 - (29 * 15) },
        { width: 2560.0, itemCount: 20, expected: 2560.0 - (29 * 20) },
        { width: 3440.0, itemCount: 25, expected: 3440.0 - (29 * 25) },
      ]

      manyItemsCases.forEach(({ width, itemCount, expected }) => {
        const result = width - ITEM_TOTAL_WIDTH * itemCount
        expect(result).toBeCloseTo(expected, 2)
        expect(result).toBeGreaterThan(0) // Should still be positive
      })
    })

    it('should handle zero items edge case', () => {
      const accordionWidth = 1500.75
      const result = accordionWidth - ITEM_TOTAL_WIDTH * 0
      expect(result).toBeCloseTo(accordionWidth, 2)
    })
  })
})